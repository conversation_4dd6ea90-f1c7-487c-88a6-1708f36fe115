<template>
  <div class="space-y-4">
    <div v-if="!filters || Object.keys(filters).length === 0" class="text-center p-2 text-zinc-500">
      No filters defined yet. Add a filter to restrict which data appears in this view.
    </div>
    <div v-else>
      <div v-for="(filterList, sourceColId) in filters" :key="sourceColId" class="mb-4">
        <div class="border border-zinc-200 shadow-xs rounded-lg">
          <div class="bg-zinc-100 p-2 rounded-t-lg text-sm font-medium flex items-center">
            <component :is="getColumnIcon(getColumnType(sourceColId))" class="size-4 mr-2 text-zinc-500"/>
            <span class="flex-1">{{ getColumnHeader(sourceColId) }}</span>
            <X
                class="size-5 text-zinc-500 hover:text-red-500 cursor-pointer"
                @click="$emit('remove-filter-for-column', sourceColId)"/>
          </div>
          <div class="p-2">
            <div v-for="(filter, index) in filterList" :key="index" class="mb-2 last:mb-0">
              <div class="flex items-center">
                <div class="flex-1">
                  <component
                      :is="getFilterComponent(sourceColId)"
                      :column="getColumnById(sourceColId)"
                      :model-value="filterList[index]"
                      @update:model-value="$emit('update-filter', sourceColId, index, $event)"
                      :inline="true"
                  />
                </div>
                <div class="shrink-0 ml-2">
                  <X
                      class="size-4 text-zinc-500 hover:text-red-500 cursor-pointer"
                      @click="$emit('remove-filter', sourceColId, index)"/>
                </div>
              </div>
            </div>
            <div class="mt-2 flex items-center">
              <div class="flex-1 border-t border-zinc-200"></div>
              <base-btn is-text class="mx-2" @click="$emit('add-filter-to-column', sourceColId)">OR</base-btn>
              <div class="flex-1 border-t border-zinc-200"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="showAddFilterButton" class="w-full">
      <select
          v-model="selectedFilterColumnId"
          @change="$emit('add-filter-to-column', selectedFilterColumnId); selectedFilterColumnId = ''"
          class="shadow-xs sm:text-sm focus:ring-fuchsia-500 focus:border-fuchsia-500 border-gray-300 rounded-md">
        <option value="">Add filter on column...</option>
        <option
            v-for="col in availableFilterColumns"
            :key="col.id"
            :value="col.id">
          {{ getColumnAutoHeader(col.id) }}
        </option>
      </select>
    </div>
  </div>
</template>

<script>
import { X } from 'lucide-vue-next'
import BaseBtn from '@/components/buttons/BaseBtn.vue'
import { getColumnIcon } from '@/utils/colTypeComponents.js'
import { isNumericType } from '@/utils/formats.js'
import BooleanFilter from '@/components/menus/filters/BooleanFilter.vue'
import TextFilter from '@/components/menus/filters/TextFilter.vue'
import NumericFilter from '@/components/menus/filters/NumericFilter.vue'
import DateTimeFilter from '@/components/menus/filters/DateTimeFilter.vue'

export default {
  name: 'FiltersTab',
  emits: ['remove-filter-for-column', 'remove-filter', 'add-filter-to-column', 'update-filter'],
  components: {
    BaseBtn,
    X,
    BooleanFilter,
    TextFilter,
    NumericFilter,
    DateTimeFilter
  },
  props: {
    filters: {
      type: Object,
      required: true
    },
    tableColumns: {
      type: Array,
      required: true
    },
    getColumnAutoHeader: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      selectedFilterColumnId: ''
    }
  },
  computed: {
    availableFilterColumns() {
      // Return columns that don't already have filters
      const existingFilterColumns = this.filters ? Object.keys(this.filters).map(id => Number(id)) : []
      return this.tableColumns.filter(col => !existingFilterColumns.includes(col.id))
    },

    showAddFilterButton() {
      // Only show the add filter button if there are columns available to filter
      return this.availableFilterColumns.length > 0
    }
  },
  methods: {
    getColumnById(colId) {
      return this.tableColumns.find(col => col.id === Number(colId))
    },

    getColumnType(colId) {
      const col = this.getColumnById(colId)
      return col ? col.type : 'text'
    },

    getColumnHeader(colId) {
      return this.getColumnAutoHeader(Number(colId))
    },

    getColumnIcon(type) {
      return getColumnIcon(type)
    },

    getFilterComponent(colId) {
      const type = this.getColumnType(colId)

      if (type === 'boolean') {
        return BooleanFilter
      } else if (type === 'text') {
        return TextFilter
      } else if (isNumericType(type)) {
        return NumericFilter
      } else if (type === 'datetime') {
        return DateTimeFilter
      }

      return TextFilter
    },


  }
}
</script>
