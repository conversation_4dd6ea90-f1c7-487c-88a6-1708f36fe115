<template>
  <div class="space-y-4">
    <sortable
        :list="columns"
        item-key="id"
        :options="sortableOptions"
        @end="$emit('column-sort-update', $event)"
    >
      <template #item="{ element: column, index }">
        <div
            :key="index"
            class="border-x border-b border-zinc-200 shadow-xs flex cursor-pointer" :class="[
              index === 0 ? 'rounded-t-lg border-t' : '',
              index === columns.length - 1 ? 'rounded-b-lg' : ''
            ]">
          <div
              class="drag-handle p-2 flex items-center cursor-move text-zinc-400 bg-zinc-100 hover:text-zinc-600">
            <GripVertical class="size-4"/>
          </div>
          <div v-if="activeColumnId === column.id" class="p-2 flex-1 flex">
            <div class="flex-1">
              <form-grid-row class="pt-0!" label="Column">
                <select
                    v-model.number="column.sourceColumnId"
                    class="block focus:ring-fuchsia-500 focus:border-fuchsia-500 w-full shadow-xs sm:text-sm border-gray-300 rounded-md">

                  <option
                      v-if="viewType === 'group'"
                      value="id"
                      key="id">Row count
                  </option>
                  <option
                      v-for="(col, index) in validTableColumns"
                      :value="col.id"
                      :key="index">{{ getColumnAutoHeader(col.id) }}
                  </option>
                </select>
              </form-grid-row>

              <form-grid-row
                  v-if="viewType === 'group' && column.sourceColumnId !== 'id' && columnAggregates(column.sourceColumnId).length"
                  label="Summarize by">
                <select
                    v-model="column.summarize"
                    class="block focus:ring-fuchsia-500 focus:border-fuchsia-500 w-full shadow-xs sm:text-sm border-gray-300 rounded-md">
                  <option
                      v-for="agg in columnAggregates(column.sourceColumnId)"
                      :value="agg.key"
                      :key="agg.key">{{ agg.label }}
                  </option>

                </select>
              </form-grid-row>

              <form-grid-row v-if="column.sourceColumnId !== undefined" label="Column header">
                <placeholder-input
                    v-model="column.header"
                    :placeholder="getSummarizeAutoHeader(index)"/>
              </form-grid-row>
            </div>
            <div class="shrink-0">
              <X
                  class="size-6 ml-4 mt-2 text-zinc-500 hover:text-red-500 cursor-pointer"
                  @click="$emit('remove-column', index)"/>
            </div>
            <div class="shrink-0">
              <CheckCircle
                  class="size-6 ml-2 mt-2 text-zinc-500 hover:text-green-500 cursor-pointer"
                  :class="{
                    invisible: !columnIsValid(column)
                  }"
                  @click="$emit('toggle-active-column', undefined)"/>
            </div>
          </div>
          <div v-else
               class="p-2 grow text-sm hover:bg-zinc-50"
               @click="$emit('toggle-active-column', column.id)">
            {{ column.header || getSummarizeAutoHeader(index) }}
          </div>
        </div>
      </template>
    </sortable>
    <base-btn :icon-component="Plus" @click="$emit('add-column')">Add column to the report...</base-btn>
  </div>
</template>

<script>
import { markRaw } from 'vue'
import { GripVertical, Plus, X, CheckCircle } from 'lucide-vue-next'
import { Sortable } from 'sortablejs-vue3'
import FormGridRow from '@/components/forms/FormGridRow.vue'
import PlaceholderInput from '@/components/forms/input/PlaceholderInput.vue'
import BaseBtn from '@/components/buttons/BaseBtn.vue'
import { getAggregateLabelsForColType } from '@/utils/aggregation.js'

export default {
  name: 'ColumnsTab',
  emits: ['column-sort-update', 'remove-column', 'toggle-active-column', 'add-column'],
  components: {
    Sortable,
    FormGridRow,
    PlaceholderInput,
    BaseBtn,
    GripVertical,
    X,
    CheckCircle
  },
  props: {
    columns: {
      type: Array,
      required: true
    },
    activeColumnId: {
      type: [String, Number],
      default: undefined
    },
    viewType: {
      type: String,
      required: true
    },
    validTableColumns: {
      type: Array,
      required: true
    },
    tableColumns: {
      type: Array,
      required: true
    },
    getColumnAutoHeader: {
      type: Function,
      required: true
    },
    getSummarizeAutoHeader: {
      type: Function,
      required: true
    },
    columnIsValid: {
      type: Function,
      required: true
    },
    sortableOptions: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      Plus: markRaw(Plus)
    }
  },
  methods: {
    columnAggregates(sourceColumnId) {
      const col = this.tableColumns.find(col => col.id === sourceColumnId)
      return (col?.type) ? getAggregateLabelsForColType(col.type) : []
    }
  }
}
</script>
