<template>
  <base-modal ref="base" :title="modalTitle" class="min-h-144" :disable-submit="!formIsValid">
    <template #default>
      <div class="flex-1">
        <div class="rounded-md">
          <placeholder-input v-model="view.title" :placeholder="placeholderTitle"/>
        </div>
        <div>
          <div class="mt-4 border-b border-zinc-200">
            <nav class="-mb-px flex space-x-4" aria-label="Tabs">
              <a
                  v-for="(tab, index) in tabs"
                  :key="tab.id"
                  :tabindex="index + 2"
                  @click="activeTab = tab.id"
                  :class="[
                      activeTab === tab.id ? 'border-fuchsia-500 text-fuchsia-600' : 'border-transparent text-zinc-500 hover:border-zinc-300 hover:text-zinc-700',
                      'whitespace-nowrap border-b-2 px-2 py-4 text-sm font-medium focus:outline-hidden cursor-pointer']"
                  :aria-current="activeTab === tab.id ? 'page' : undefined">{{ tab.text }}</a>
            </nav>
          </div>
        </div>

        <div class="py-4">
          <view-type-tab
              v-if="activeTab === 'type'"
              :view-type="viewType"
              @set-view-type="setViewType"
          />

          <groups-tab
              v-if="activeTab === 'groups'"
              :groups="view.groups"
              :active-group-id="activeGroupId"
              :table-columns="tableStoreColumns"
              :get-column-auto-header="tableStoreGetColumnAutoHeader"
              :sortable-options="sortableOptions"
              @group-sort-update="groupSortUpdate"
              @remove-group="removeGroup"
              @toggle-active-group="toggleActiveGroup"
              @add-group="addGroup"
          />

          <columns-tab
              v-if="activeTab === 'columns'"
              :columns="view.columns"
              :active-column-id="activeColumnId"
              :view-type="viewType"
              :valid-table-columns="validTableColumns"
              :table-columns="tableStoreColumns"
              :get-column-auto-header="tableStoreGetColumnAutoHeader"
              :get-summarize-auto-header="getSummarizeAutoHeader"
              :column-is-valid="columnIsValid"
              :sortable-options="sortableOptions"
              @column-sort-update="columnSortUpdate"
              @remove-column="removeColumn"
              @toggle-active-column="toggleActiveColumn"
              @add-column="addColumn"
          />

          <filters-tab
              v-if="activeTab === 'filters'"
              :filters="view.filters"
              :table-columns="tableStoreColumns"
              :get-column-auto-header="tableStoreGetColumnAutoHeader"
              @remove-filter-for-column="removeFilterForColumn"
              @remove-filter="removeFilter"
              @add-filter-to-column="addFilterToColumn"
              @update-filter="updateFilter"
          />

          <sorting-tab
              v-if="activeTab === 'sorting'"
              :sort-columns="sortColumns"
              :available-sort-columns="availableSortColumns"
              :sortable-options="sortableOptions"
              :table-columns="tableStoreColumns"
              @sort-column-sort-update="sortColumnSortUpdate"
              @remove-sort-column="removeSortColumn"
              @add-sort-column="addSortColumn"
          />
        </div>
      </div>
    </template>

    <template #buttons>
      <base-btn is-submit class="ml-2" type="primary" :disabled="!formIsValid" @click.prevent="insertAndClose">
        {{ this.view.id ? 'Edit View' : 'Add View' }}
      </base-btn>
      <base-btn @click="$refs.base.close">Cancel</base-btn>
      <div v-if="this.view.id" class="flex-1">
        <base-btn type="warning" is-text @click="confirmDelete">Delete view</base-btn>
      </div>
    </template>
  </base-modal>

  <teleport to="body">
    <confirm-dialog
        title="Delete View"
        text="Are you sure you want to delete this view?"
        ref="deleteViewDialog"
        @confirm="$emit('delete-view', this.view.id)"
    />
  </teleport>
</template>

<script>

import { markRaw } from 'vue'
import { moveInArray } from '@/utils/helpers.js'
import BaseBtn from '@/components/buttons/BaseBtn.vue'
import BaseModal from '@/components/modal/BaseModal.vue'
import { mapActions, mapState } from 'pinia'
import { createView, createViewColumn, createViewGroup, getNextViewColumnId, useTableStore } from '@/stores/table.js'
import { Plus } from 'lucide-vue-next'
import PlaceholderInput from '@/components/forms/input/PlaceholderInput.vue'
import { viewTypes } from '@/db/grouping.js'
import { aggregates } from '@/utils/aggregation.js'
import { getSortColumns } from '@/db/query/utils.js'
import ConfirmDialog from '@/components/modal/ConfirmDialog.vue'
import ViewTypeTab from './ViewTypeTab.vue'
import GroupsTab from './GroupsTab.vue'
import FiltersTab from './FiltersTab.vue'
import ColumnsTab from './ColumnsTab.vue'
import SortingTab from './SortingTab.vue'

export default {
  emits: ['change-view', 'add-view', 'delete-view'],
  components: {
    ConfirmDialog,
    PlaceholderInput,
    BaseModal,
    BaseBtn,
    ViewTypeTab,
    GroupsTab,
    FiltersTab,
    ColumnsTab,
    SortingTab
  },

  data () {
    return {
      Plus: markRaw(Plus),
      view: undefined,
      viewType: undefined,
      activeTab: 'type',
      activeColumnId: undefined,
      activeGroupId: undefined,
      sortColumns: [],
      sortableOptions: {
        handle: '.drag-handle',
        animation: 150,
        ghostClass: 'sortable-ghost',
        dragClass: 'sortable-drag'
      }
    }
  },

  computed: {
    ...mapState(useTableStore, {
      tableStoreId: 'id',
      tableStoreColumns: 'columns'
    }),

    modalTitle () {
      return this.view?.id ? 'Edit View' : 'Add View'
    },

    formIsValid () {
      if (this.viewType === 'filter') {
        // every view.column must have a valid source column; id is not valid
        return this.view.columns.every(col => this.validTableColumns.find(c => c.id === col.sourceColumnId))
      }
      if (this.viewType === 'group') {
        // every view.column must have summarize set or be 'id', and there must be at least one group
        return this.view.columns.every(
                // Every column must be 'id' or a valid column, and it all must have a summarize
                col =>
                    (col.sourceColumnId === 'id' || this.validTableColumns.find(c => c.id === col.sourceColumnId)) &&
                    (col.summarize !== undefined)
            ) &&
            // Every group must have a valid source column
            this.view.groups.every(group => this.validTableColumns.find(c => c.id === group.sourceColumnId))
      }
      return false
    },

    tabs () {
      const tabs = [
        { id: 'type', text: 'View Type' }
      ]
      if (this.viewType === 'group') {
        tabs.push({ id: 'groups', text: 'Groups' })
      }
      if (this.viewType === 'filter' || this.viewType === 'group') {
        tabs.push({ id: 'filters', text: 'Filters' })
        tabs.push({ id: 'columns', text: 'Columns' })
        tabs.push({ id: 'sorting', text: 'Sorting' })
      }
      return tabs
    },

    placeholderTitle () {
      return viewTypes[this.viewType]?.label || 'Enter a View Title'
    },

    validTableColumns () {
      return this.tableStoreColumns
    },

    availableSortColumns () {
      const getHeaderForItem = (item, type) =>
          item.header || (
              type === 'group'
              ? this.tableStoreGetColumnAutoHeader(item.sourceColumnId)
              : this.getSummarizeAutoHeader(this.view.columns.indexOf(item))
          )

      const mapToSortableColumn = (type) => (item) => ({
        id: item.id,
        sourceColumnId: item.sourceColumnId,
        header: getHeaderForItem(item, type),
        type
      })

      const hasValidSourceColumn = (item) => item.sourceColumnId !== undefined
      const isNotAlreadySorted = (col) => !this.sortColumns.some(sc => sc.id === col.id)

      return [
        ...(this.view.groups || []).filter(hasValidSourceColumn).map(mapToSortableColumn('group')),
        ...(this.view.columns || []).filter(hasValidSourceColumn).map(mapToSortableColumn('column'))
      ].filter(isNotAlreadySorted)
    },

    cleanView () {
      /* Cleans up the view for submitting */

      const view = JSON.parse(JSON.stringify(this.view))

      // If filter view, remove any groups; this can happen if changing group to view
      if (this.viewType === 'filter') {
        view.groups = []
      }

      // For 'row counts', force summarize of count
      view.columns.forEach(col => {
        if (col.sourceColumnId === 'id') {
          col.summarize = 'count'
        }
      })

      // Give every column a header if it doesn't have one
      view.columns.forEach((col, index) => {
        if (col.header === undefined || col.header === '') {
          col.header = this.getSummarizeAutoHeader(index)
        }
      })

      // Always set a title
      if (view.title === undefined || view.title === '') {
        view.title = this.placeholderTitle
      }

      // Go through all filters and remove any that are null
      view.filters = Object.fromEntries(
          Object.entries(view.filters)
              .map(([colId, filters]) => [colId, filters.filter(Boolean)])
              .filter(([colId, filters]) => filters.length > 0)
      )

      // Clear existing sort properties from all groups and columns
      view.groups.forEach(group => {
        delete group.sort
      })
      view.columns.forEach(column => {
        delete column.sort
      })

      // Apply sorting from sortColumns array
      this.sortColumns.forEach((sortCol, index) => {
        const group = view.groups.find(g => g.id === sortCol.id)
        const column = view.columns.find(c => c.id === sortCol.id)

        if (group) {
          group.sort = { order: index, desc: sortCol.desc }
        } else if (column) {
          column.sort = { order: index, desc: sortCol.desc }
        }
      })

      return view
    }
  },

  watch: {
    'view.groups': {
      handler (groups) {
        if (!groups) return

        // Check each group and reset 'by' property if column type doesn't support it
        groups.forEach(group => {
          if (group.sourceColumnId !== undefined && group.by !== undefined) {
            const col = this.tableStoreColumns.find(col => col.id === group.sourceColumnId)
            // If column is not datetime but has a groupBy value, reset it
            if (col && col.type !== 'datetime') {
              group.by = undefined
            }
          }
        })

        // Clean up sort columns that reference deleted groups
        this.cleanupInvalidSortColumns()
      },
      deep: true
    },

    'view.columns': {
      handler () {
        // Clean up sort columns that reference deleted columns
        this.cleanupInvalidSortColumns()
      },
      deep: true
    }
  },

  methods: {
    ...mapActions(useTableStore, {
      tableStoreGetColumnAutoHeader: 'getColumnAutoHeader'
    }),

    columnIsValid (column) {
      // Allow 'id' as a source column only if grouped view
      if (this.viewType === 'group' && column.sourceColumnId === 'id') {
        return true
      }
      return this.validTableColumns.find(col => col.id === column.sourceColumnId) &&
          (column.summarize !== undefined || column.sourceColumnId === 'id')
    },

    getSummarizeAutoHeader (colIndex) {
      const col = this.view.columns[colIndex]
      if (col.sourceColumnId === 'id') {
        return 'Row count'
      }
      const colAutoHeader = this.tableStoreGetColumnAutoHeader(col.sourceColumnId)
      if (col.summarize) {
        const aggLabel = aggregates[col.summarize].label
        return `${colAutoHeader} (${aggLabel})`
      }
      return colAutoHeader
    },

    open (view) {
      this.activeColumnId = undefined
      this.activeGroupId = undefined
      this.selectedFilterColumnId = ''
      if (view) {
        this.view = JSON.parse(JSON.stringify(view))
        if (!this.view.filters) {
          this.view.filters = {}
        }
        this.viewType = this.sniffViewType(view)
        this.activeTab = this.tabs[1].id
        // Hydrate sort columns from the view
        this.hydrateSortColumns()
      } else {
        this.view = createView(this.tableStoreId)
        this.view.filters = {}
        this.viewType = undefined
        this.activeTab = this.tabs[0].id
        this.sortColumns = []
      }
      this.$refs.base.open()
    },

    hydrateSortColumns () {
      // Use getSortColumns to extract sorting information from groups and columns
      const allColumns = [...(this.view.groups || []), ...(this.view.columns || [])]
      const sortedColumns = getSortColumns(allColumns)

      this.sortColumns = sortedColumns.map(sortCol => {
        const group = this.view.groups?.find(g => g.id === sortCol.id)
        const column = this.view.columns?.find(c => c.id === sortCol.id)
        const item = group || column

        return {
          id: sortCol.id,
          desc: sortCol.desc,
          header: item?.header || (group ? this.tableStoreGetColumnAutoHeader(item.sourceColumnId) : this.getSummarizeAutoHeader(this.view.columns.indexOf(item))),
          type: group ? 'group' : 'column',
          sourceColumnId: item?.sourceColumnId
        }
      })
    },

    cleanupInvalidSortColumns () {
      // Remove sort columns that no longer exist in groups or columns
      const validIds = [
        ...(this.view.groups || []).map(g => g.id),
        ...(this.view.columns || []).map(c => c.id)
      ]

      this.sortColumns = this.sortColumns.filter(sortCol => validIds.includes(sortCol.id))
    },

    confirmDelete () {
      this.$refs.base.close()
      this.$refs.deleteViewDialog.open()
    },

    insertAndClose () {
      this.$refs.base.close()
      if (this.view.id) {
        this.$emit('change-view', this.cleanView)
      } else {
        this.$emit('add-view', this.cleanView)
      }
    },

    sniffViewType (view) {
      if (view) {
        if (view.groups.length) {
          return 'group'
        } else {
          return 'filter'
        }
      }
      return undefined
    },

    setViewType (type) {
      this.viewType = type
      this.activeTab = this.tabs[1].id
    },



    columnSortUpdate (event) {
      moveInArray(this.view.columns, event.oldIndex, event.newIndex)
    },

    addColumn () {
      const column = createViewColumn(undefined)
      column.id = getNextViewColumnId(this.view)
      this.view.columns.push(column)
      this.toggleActiveColumn(column.id)
    },

    addGroup () {
      const group = createViewGroup(undefined)
      group.id = getNextViewColumnId(this.view)
      this.view.groups.push(group)
      this.toggleActiveGroup(group.id)
    },

    removeColumn (index) {
      this.view.columns.splice(index, 1)
    },

    toggleActiveColumn (id) {
      const index = this.view.columns.findIndex(column => column.id === this.activeColumnId)
      if (index > -1 && this.view.columns[index].sourceColumnId === undefined) {
        this.view.columns.splice(index, 1)
      }
      if (this.activeColumnId === id || id === undefined) {
        this.activeColumnId = undefined
      } else {
        this.activeColumnId = id
      }
    },

    toggleActiveGroup (id) {
      // If current active group doesn't have a column set, remove it
      const index = this.view.groups.findIndex(group => group.id === this.activeGroupId)
      if (index !== -1 && this.view.groups[index].sourceColumnId === undefined) {
        this.view.groups.splice(index, 1)
      }
      if (this.activeGroupId === id || id === undefined) {
        this.activeGroupId = undefined
      } else {
        this.activeGroupId = id
      }
    },

    groupSortUpdate (event) {
      moveInArray(this.view.groups, event.oldIndex, event.newIndex)
    },

    removeGroup (index) {
      this.view.groups.splice(index, 1)
    },

    // Filter-related methods
    addFilterToColumn (colId) {
      if (!this.view.filters) {
        this.view.filters = {}
      }
      if (!this.view.filters[colId]) {
        this.view.filters[colId] = []
      }

      let defaultFilter = { operator: 'equals' }
      this.view.filters[colId].push(defaultFilter)
    },

    removeFilter (colId, index) {
      if (this.view.filters && this.view.filters[colId]) {
        this.view.filters[colId].splice(index, 1)

        // If no more filters for this column, remove the column entry
        if (this.view.filters[colId].length === 0) {
          delete this.view.filters[colId]
        }
      }
    },

    removeFilterForColumn (colId) {
      if (this.view.filters) {
        delete this.view.filters[colId]
      }
    },

    updateFilter (colId, index, newFilter) {
      if (this.view.filters && this.view.filters[colId] && this.view.filters[colId][index] !== undefined) {
        this.view.filters[colId][index] = newFilter
      }
    },

    // Sorting-related methods
    addSortColumn (columnId) {
      if (!columnId) return
      const numericId = Number(columnId)
      const availableColumn = this.availableSortColumns.find(col => col.id === numericId)
      if (availableColumn) {
        this.sortColumns.push({
          id: availableColumn.id,
          desc: false,
          header: availableColumn.header,
          type: availableColumn.type,
          sourceColumnId: availableColumn.sourceColumnId
        })
      }
    },

    removeSortColumn (index) {
      this.sortColumns.splice(index, 1)
    },

    sortColumnSortUpdate (event) {
      moveInArray(this.sortColumns, event.oldIndex, event.newIndex)
    }

  }
}
</script>

<style scoped>
/*noinspection CssUnusedSymbol*/
.overflow-y-auto {
  scrollbar-width: thin;
}

/*noinspection CssUnusedSymbol*/
.sortable-ghost {
  opacity: 0.5;
  background: #f3f4f6;
}

/*noinspection CssUnusedSymbol*/
.sortable-drag {
  background: white;
}

.drag-handle {
  touch-action: none;
}
</style>
