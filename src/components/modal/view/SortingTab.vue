<template>
  <div class="space-y-4">
    <div v-if="sortColumns.length === 0" class="text-center p-2 text-zinc-500">
      No sorting defined yet. Add columns to sort the data in this view.
    </div>
    <div v-else>
      <sortable
          :list="sortColumns"
          item-key="id"
          :options="sortableOptions"
          @end="$emit('sort-column-sort-update', $event)"
      >
        <template #item="{ element: sortCol, index }">
          <div
              :key="index"
              class="border-x border-b border-zinc-200 shadow-xs flex cursor-pointer" :class="[
                index === 0 ? 'rounded-t-lg border-t' : '',
                index === sortColumns.length - 1 ? 'rounded-b-lg' : ''
              ]">
            <div
                class="drag-handle p-2 flex items-center cursor-move text-zinc-400 bg-zinc-100 hover:text-zinc-600">
              <GripVertical class="size-4"/>
            </div>
            <div class="p-2 flex-1 flex items-center">
              <div class="flex-1">
                <div class="text-sm font-medium">{{ sortCol.header }}</div>
                <div class="text-xs text-zinc-500">{{ sortCol.type === 'group' ? 'Group' : 'Column' }}</div>
              </div>
              <div class="shrink-0 flex items-center space-x-2">
                <base-toggle
                    v-model="sortCol.desc"
                    :left-label="getSortLabels(sortCol).asc"
                    :label="getSortLabels(sortCol).desc"
                />
                <X
                    class="size-5 ml-2 text-zinc-500 hover:text-red-500 cursor-pointer"
                    @click="$emit('remove-sort-column', index)"/>
              </div>
            </div>
          </div>
        </template>
      </sortable>
    </div>
    <div v-if="availableSortColumns.length > 0" class="w-full">
      <select
          @change="$emit('add-sort-column', $event.target.value); $event.target.value = ''"
          class="shadow-xs sm:text-sm focus:ring-fuchsia-500 focus:border-fuchsia-500 border-gray-300 rounded-md">
        <option value="">Add column to sort...</option>
        <option
            v-for="col in availableSortColumns"
            :key="col.id"
            :value="col.id">
          {{ col.header }} ({{ col.type === 'group' ? 'Group' : 'Column' }})
        </option>
      </select>
    </div>
  </div>
</template>

<script>
import { GripVertical, X } from 'lucide-vue-next'
import { Sortable } from 'sortablejs-vue3'
import BaseToggle from '@/components/forms/input/BaseToggle.vue'
import { columnTypes } from '@/utils/formats.js'

export default {
  name: 'SortingTab',
  emits: ['sort-column-sort-update', 'remove-sort-column', 'add-sort-column'],
  components: {
    Sortable,
    BaseToggle,
    GripVertical,
    X
  },
  props: {
    sortColumns: {
      type: Array,
      required: true
    },
    availableSortColumns: {
      type: Array,
      required: true
    },
    sortableOptions: {
      type: Object,
      required: true
    },
    tableColumns: {
      type: Array,
      required: true
    }
  },
  methods: {
    getSortLabels(sortCol) {
      // Find the source column to get its type
      const sourceColumn = this.tableColumns.find(col => col.id === sortCol.sourceColumnId)

      if (sourceColumn && sourceColumn.type && columnTypes[sourceColumn.type]) {
        return columnTypes[sourceColumn.type].sortLabels
      }

      // Fallback to generic labels if column type not found
      return {
        asc: 'ASC',
        desc: 'DESC'
      }
    }
  }
}
</script>
